# KOL-Hub 内部KOL管理平台 - 技术规格说明文档

## 文档一：技术架构与选型规格说明

**项目名称：** KOL-Hub 内部KOL管理平台

**文档用途：** 本文档详细定义了项目前端的技术栈、架构模式和开发构建工具，主要面向前端开发团队、架构师和技术负责人。

### 2.1. 前端技术栈

* **核心框架：** **React v18+**
  * **理由：** React 生态成熟，性能优异，能满足企业级应用需求，且社区支持广泛，方便解决开发中遇到的问题。

* **强制规定：前端路由：** **React Router v6**
  * **理由：** 作为 React 社区的事实标准，它提供强大的声明式路由和嵌套路由能力，简洁高效。项目强制使用此路由方案以确保一致性。

* **强制规定：状态管理：** **Redux Toolkit (RTK)**
  * **理由：** RTK 是 Redux 官方推荐的工具集，极大地简化了 Redux 的使用和配置，通过 `createSlice` 和 `createAsyncThunk` 有效管理复杂状态和异步操作。项目强制使用 RTK 来统一状态管理模式。
  * **Store 结构规划：**
    * `userSlice`: 管理当前登录用户信息、权限。
    * `kolsSlice`: 管理 KOL 列表、分页、筛选条件和单个 KOL 详情。
    * `campaignsSlice`: 管理活动列表、看板状态和单个活动详情。
    * `discoverySlice`: 管理 KOL 发现页的筛选条件和搜索结果。
    * `uiSlice`: 管理全局 UI 状态，如侧边栏折叠状态、模态框可见性、全局加载状态。

* **数据请求：** **Axios**
  * **理由：** 功能强大且易于使用，支持 Promise、请求/响应拦截器，方便统一处理认证和错误。将封装一个 API 服务层。

* **强制规定：样式方案：** **Styled-components + Ant Design v5**
  * **理由：** **Ant Design** 提供高质量的企业级 UI 组件，可大幅加速开发并保证 UI/UX 的一致性。**Styled-components** 允许组件级别的样式封装和动态样式，满足定制化需求。项目强制使用 Ant Design 作为主要的 UI 组件库，并通过 Styled-components 进行必要的样式扩展。
  * **主题定制：** 使用 AntD 的 `<ConfigProvider>` 进行全局主题配置，确保设计一致。

* **强制规定：类型安全：** **TypeScript**
  * **理由：** **项目强制使用 TypeScript**。它通过类型检查能有效减少运行时错误，提升代码质量和可维护性，对于团队协作尤其重要。所有新编写和修改的代码必须符合 TypeScript 类型规范。

### 2.2. 开发与构建工具

* **包管理器：** **Yarn**
* **构建工具：** **Vite**
  * **理由：** 提供极速的开发体验，包括快速冷启动和热模块更新（HMR）。

* **强制规定：代码规范：** **ESLint + Prettier**
  * **理由：** **项目强制使用 ESLint 和 Prettier**，并遵循团队预设的配置。这将确保所有代码都符合统一的风格规范，提高可读性和可维护性。
  * **ESLint 配置：** `eslint-config-airbnb`, `eslint-plugin-react-hooks`
  * **Prettier：** 用于自动化代码格式化，确保团队代码风格统一。

* **强制规定：版本控制：** **Git (遵循 Git Flow 工作流)**
  * **理由：** **项目强制遵循 Git Flow 工作流**。这是团队协作的必要规范，能有效管理并行开发、版本发布和紧急修复，避免代码冲突和版本混乱。

### 2.3. 代码质量与测试

* **强制规定：测试策略：**
  * **单元/组件测试：** 采用 **Vitest** (或 Jest) 结合 **React Testing Library** 进行核心业务逻辑和关键组件的测试，确保功能稳定。**关键模块和复杂组件必须编写单元/组件测试。**

* **文档：**
  * 对于可复用的通用组件或工具函数，建议编写**必要的代码注释和 README 说明**，方便团队成员理解和使用。

### 2.4. 开发实践与流程

* **强制规定：代码审查 (Code Review)：**
  * **理由：** **所有代码合并请求（Pull Request）都必须经过至少一位团队成员的代码审查并通过后方可合并。** 这项强制实践旨在发现潜在问题、促进知识共享和确保代码质量。

---

## 文档二：系统布局与设计系统规格说明

**文档用途：** 本文档定义了应用的整体布局结构和 UI 设计规范（Design System），主要面向 UI/UX 设计师和前端开发人员，以确保视觉和交互的统一性。

### 3.1. 整体布局 (Layout)

* **类型：** 单页应用（SPA）
* **结构：** "品"字形经典后台布局
  * **顶部导航栏 (Top Navbar):**
    * **高度:** 64px
    * **内容：** 左侧为 Logo 和系统名称，右侧为通知中心图标、帮助文档入口、用户头像及下拉菜单（个人设置、退出登录）。
  * **左侧侧边栏 (Sidebar):**
    * **展开宽度:** 220px
    * **折叠宽度:** 80px
    * **功能：** 包含应用的主要导航链接，带图标和文本。当前激活的菜单项高亮显示。
  * **主内容区 (Main Content):**
    * **内边距 (Padding):** 24px
    * 包含面包屑导航（Breadcrumbs），页面标题，和实际内容。

### 3.2. 响应式设计

* **目标：** 优先保证桌面端体验，对平板设备进行适配。
* **断点 (Breakpoints)：**
  * **桌面 (Desktop):** > 1200px
  * **平板 (Tablet):** 768px - 1199px (侧边栏可能默认收起)
  * **移动端 (Mobile):** < 768px (非核心支持，但需保证基本可用性，侧边栏将变为抽屉式)

### 3.3. 设计系统 (Design System)

* **颜色规范 (Color Palette):**
  * **Primary (品牌蓝):** `#1890ff` (链接、主按钮、高亮)
  * **Success (成功绿):** `#52c41a` (成功提示、完成状态)
  * **Warning (警告黄):** `#faad14` (警告信息、待确认状态)
  * **Error (错误红):** `#f5222d` (错误提示、失败状态)
  * **Text (文本色):**
    * `#262626` (主标题)
    * `#595959` (正文/次级文本)
    * `#8c8c8c` (辅助/禁用文本)
  * **Layout (布局色):**
    * `#f0f2f5` (主背景)
    * `#ffffff` (卡片/内容区背景)
    * `#d9d9d9` (边框)

* **排版指南 (Typography):**
  * **字体家族:** `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'`
  * **字号层级:**
    * H1: 30px, Medium
    * H2: 24px, Medium
    * H3: 20px, Medium
    * H4: 16px, Medium
    * Body (正文): 14px, Regular
    * Caption (辅助文字): 12px, Regular

* **间距规范 (Spacing):**
  * 基于 **8px 的网格系统**。所有外边距 (margin) 和内边距 (padding) 应为 8 的倍数 (8px, 16px, 24px, 32px ...)。

* **图标库 (Iconography):**
  * 使用 **Ant Design Icons** (`@ant-design/icons`)。保持图标风格一致性，非必要不引入其他图标库。

---

## 文档三：关键组件与页面规格说明

**文档用途：** 本文档详细描述了项目中各个核心页面和组件的功能需求、用户故事和布局，主要面向产品经理、前端和后端开发人员及测试人员。

### 4.1. 登录页 (Login Page)

* **用户故事：** 作为一个用户，我希望能通过我的公司邮箱和密码安全地登录系统。
* **功能需求：**
  * 包含 Logo、系统标题。
  * 用户名（邮箱）输入框、密码输入框。
  * "记住我"复选框。
  * "登录"按钮。
  * 处理 API 返回的认证成功/失败信息。
* **布局：** 页面水平垂直居中，卡片式表单。

### 4.2. 仪表板 (Dashboard)

* **用户故事：** 作为一个运营经理，我希望一登录就能看到最重要的 KPI，以便快速了解业务概况。
* **功能需求：**
  * **KPI 卡片区：** 展示"总 KOL 数"、"本月新增 KOL"、"进行中活动数"、"活动总曝光量"四个核心指标。
  * **KOL 趋势图：** 使用折线图展示过去 6 个月每月新增 KOL 数量。
  * **活动状态分布：** 使用饼图或环形图展示"策划中"、"进行中"、"已完成"、"已终止"的活动数量占比。
* **布局：** 采用 24 列栅格系统。顶部为一行 4 个 KPI 卡片，下方为图表区。

### 4.3. KOL 列表页 (KOL List Page)

* **用户故事：** 作为一个市场专员，我希望能快速搜索、筛选和排序我已管理的 KOL，以便找到符合活动要求的 KOL。
* **功能需求：**
  * **搜索与高级筛选器：** 支持按名称、平台、粉丝数、标签、合作状态进行筛选。
  * **数据表格 (<Table>)：**
    * **列：** KOL 头像/昵称、平台、粉丝数、互动率、标签、状态、负责人、操作。
    * **功能：** 支持排序、分页、批量操作。
    * **按钮：** 页面右上角有"+ 新增 KOL"按钮。

### 4.4. KOL 详情页 (KOL Profile Page)

* **用户故事：** 作为一个市场专员，我希望查看一个 KOL 的全部信息，包括他的历史表现和联系方式，以便评估其合作价值。
* **功能需求：**
  * 使用**标签页 (<Tabs>)** 组织信息。
  * **Tab 1: 概览 (Overview)：** 展示核心资料和数据图表。
  * **Tab 2: 合作历史 (Campaign History)：** 展示历史活动列表。
  * **Tab 3: 内容作品 (Content Portfolio)：** 展示代表作品。
  * **Tab 4: 备注与日志 (Notes & Logs)：** 展示沟通记录。
* **布局：** 页面顶部为 KOL 姓名和返回按钮，主体为标签页结构。

### 4.5. KOL 发现页 (KOL Discovery Page)

* **用户故事：** 作为一个市场专员，我希望能在一个统一的界面中，先选择我要探索的社交媒体，再选择数据来源，然后根据精细化条件进行搜索，以便高效地发现潜在合作 KOL，并能一键将他们存入公司自己的 KOL 数据库。
* **功能需求与流程：**
  * **步骤 1: 选择社交平台 (Select Social Platform)：** 通过卡片式按钮选择"YouTube"、"Instagram"或"TikTok"。
  * **步骤 2: 选择数据源 (Select Data Source)：** 根据平台选择，展现可用的数据源（如 Modash API, ttone API）。
  * **步骤 3: 输入具体筛选条件 (Enter Specific Filters)：** 动态生成包含关键词、粉丝数、地域等条件的表单。
  * **步骤 4: 展示与处理搜索结果 (Display & Process Results)：** 结果以"KOL 简报卡片"列表呈现，每张卡片提供 "导入系统" 功能。
* **页面布局描述：** 采用分步引导式布局，上方为选择器和筛选表单，下方为搜索结果区。

---

## 总结

本规格说明文档涵盖了 KOL-Hub 项目的技术架构、设计系统和核心功能需求。所有标记为"强制规定"的技术选型和开发实践必须严格遵循，以确保项目的一致性、可维护性和代码质量。
