# KOL-Hub 设计系统规格说明

**项目名称：** KOL-Hub 内部KOL管理平台  
**文档版本：** v1.0  
**最后更新：** 2025-01-10  
**目标受众：** UI/UX设计师、前端开发人员、产品经理

## 1. 文档概述

本文档专门定义 KOL-Hub 项目的视觉设计规范、布局系统和交互标准。旨在确保整个应用的视觉一致性和用户体验统一性。

## 2. 整体布局架构

### 2.1 应用类型
- **架构模式：** 单页应用 (SPA)
- **布局类型：** "品"字形经典后台管理布局
- **响应式策略：** 桌面优先，平板适配

### 2.2 布局结构

#### 顶部导航栏 (Header)
```
高度: 64px
背景: #ffffff
边框: 1px solid #f0f0f0
阴影: 0 2px 8px rgba(0,0,0,0.06)

布局内容:
├── 左侧区域
│   ├── Logo (32x32px)
│   └── 系统名称 "KOL-Hub"
└── 右侧区域
    ├── 通知中心图标 (Badge支持)
    ├── 帮助文档入口
    └── 用户头像下拉菜单
        ├── 个人设置
        ├── 账户管理
        └── 退出登录
```

#### 左侧侧边栏 (Sidebar)
```
展开宽度: 220px
折叠宽度: 80px
背景: #001529 (深色主题)
切换动画: 0.2s ease

导航结构:
├── 仪表板 (Dashboard)
├── KOL管理
│   ├── KOL列表
│   └── KOL发现
├── 活动管理
│   ├── 活动列表
│   └── 活动分析
└── 系统设置
    ├── 用户管理
    └── 系统配置

交互状态:
- 默认: rgba(255,255,255,0.65)
- 悬停: rgba(255,255,255,0.85)
- 激活: #1890ff + 左侧3px蓝色边框
```

#### 主内容区 (Main Content)
```
内边距: 24px
背景: #f0f2f5
最小高度: calc(100vh - 64px)

内容结构:
├── 面包屑导航 (Breadcrumb)
├── 页面标题区
│   ├── 主标题 (H1)
│   ├── 副标题/描述 (可选)
│   └── 操作按钮区 (右对齐)
└── 页面内容区
```

## 3. 响应式断点系统

### 3.1 断点定义
```scss
$breakpoints: (
  xs: 0px,      // 超小屏 (手机竖屏)
  sm: 576px,    // 小屏 (手机横屏)
  md: 768px,    // 中屏 (平板竖屏)
  lg: 992px,    // 大屏 (平板横屏)
  xl: 1200px,   // 超大屏 (桌面)
  xxl: 1600px   // 超超大屏 (大桌面)
);
```

### 3.2 响应式行为
- **桌面 (≥1200px):** 完整布局，侧边栏默认展开
- **平板 (768px-1199px):** 侧边栏默认收起，可展开
- **移动端 (<768px):** 侧边栏变为抽屉式，覆盖主内容

## 4. 色彩系统 (Color Palette)

### 4.1 主色调 (Primary Colors)
```scss
// 品牌蓝色系
$primary-color: #1890ff;
$primary-1: #e6f7ff;  // 最浅
$primary-2: #bae7ff;
$primary-3: #91d5ff;
$primary-4: #69c0ff;
$primary-5: #40a9ff;
$primary-6: #1890ff;  // 主色
$primary-7: #096dd9;
$primary-8: #0050b3;
$primary-9: #003a8c;
$primary-10: #002766; // 最深
```

### 4.2 功能色彩 (Functional Colors)
```scss
// 成功色 (绿色系)
$success-color: #52c41a;
$success-bg: #f6ffed;
$success-border: #b7eb8f;

// 警告色 (黄色系)
$warning-color: #faad14;
$warning-bg: #fffbe6;
$warning-border: #ffe58f;

// 错误色 (红色系)
$error-color: #f5222d;
$error-bg: #fff2f0;
$error-border: #ffccc7;

// 信息色 (蓝色系)
$info-color: #1890ff;
$info-bg: #e6f7ff;
$info-border: #91d5ff;
```

### 4.3 中性色彩 (Neutral Colors)
```scss
// 文本色
$text-color: #262626;           // 主文本
$text-color-secondary: #595959; // 次要文本
$text-color-disabled: #8c8c8c;  // 禁用文本
$text-color-inverse: #ffffff;   // 反色文本

// 背景色
$background-color-light: #fafafa;  // 浅背景
$background-color-base: #f5f5f5;   // 基础背景
$component-background: #ffffff;     // 组件背景

// 边框色
$border-color-base: #d9d9d9;      // 基础边框
$border-color-split: #f0f0f0;     // 分割线
$border-color-inverse: #ffffff;    // 反色边框
```

## 5. 字体系统 (Typography)

### 5.1 字体族 (Font Family)
```scss
$font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
              'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
              'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
              'Noto Color Emoji';
```

### 5.2 字号层级 (Font Scale)
```scss
// 标题字号
$font-size-h1: 30px;  // 页面主标题
$font-size-h2: 24px;  // 区块标题
$font-size-h3: 20px;  // 小节标题
$font-size-h4: 16px;  // 组件标题

// 正文字号
$font-size-base: 14px;     // 基础字号
$font-size-lg: 16px;       // 大号正文
$font-size-sm: 12px;       // 小号正文

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 600;
```

### 5.3 行高系统 (Line Height)
```scss
$line-height-base: 1.5715;  // 基础行高
$line-height-lg: 1.5;       // 大文本行高
$line-height-sm: 1.66;      // 小文本行高
```

## 6. 间距系统 (Spacing)

### 6.1 基础间距单位
```scss
// 8px 网格系统
$spacing-unit: 8px;

// 间距变量
$spacing-xs: 4px;   // 0.5 * unit
$spacing-sm: 8px;   // 1 * unit
$spacing-md: 16px;  // 2 * unit
$spacing-lg: 24px;  // 3 * unit
$spacing-xl: 32px;  // 4 * unit
$spacing-xxl: 48px; // 6 * unit
```

### 6.2 组件间距规范
```scss
// 页面级间距
$page-padding: 24px;
$section-margin: 32px;

// 组件级间距
$component-padding: 16px;
$component-margin: 16px;

// 元素级间距
$element-margin: 8px;
$element-padding: 8px;
```

## 7. 图标系统 (Iconography)

### 7.1 图标库选择
- **主要图标库：** Ant Design Icons (`@ant-design/icons`)
- **图标风格：** 线性图标为主，填充图标为辅
- **图标尺寸：** 14px, 16px, 20px, 24px

### 7.2 图标使用规范
```tsx
// 导入方式
import { UserOutlined, SettingOutlined } from '@ant-design/icons';

// 尺寸规范
<UserOutlined style={{ fontSize: '16px' }} />  // 常规
<UserOutlined style={{ fontSize: '20px' }} />  // 大号
<UserOutlined style={{ fontSize: '24px' }} />  // 特大
```

## 8. 组件设计规范

### 8.1 按钮 (Button)
```scss
// 主按钮
.ant-btn-primary {
  background: $primary-color;
  border-color: $primary-color;
  height: 32px;
  padding: 4px 15px;
  border-radius: 6px;
}

// 次要按钮
.ant-btn-default {
  background: #ffffff;
  border-color: $border-color-base;
  color: $text-color;
}
```

### 8.2 表单 (Form)
```scss
// 表单项间距
.ant-form-item {
  margin-bottom: 24px;
}

// 输入框
.ant-input {
  height: 32px;
  padding: 4px 11px;
  border-radius: 6px;
  border: 1px solid $border-color-base;
}
```

### 8.3 卡片 (Card)
```scss
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
              0 1px 6px -1px rgba(0, 0, 0, 0.02),
              0 2px 4px 0 rgba(0, 0, 0, 0.02);
}
```

## 9. 动画与过渡

### 9.1 动画时长
```scss
$motion-duration-slow: 0.3s;     // 慢速动画
$motion-duration-mid: 0.2s;      // 中速动画
$motion-duration-fast: 0.1s;     // 快速动画
```

### 9.2 缓动函数
```scss
$ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 0.7);
$ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);
$ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
$ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
```

## 10. 设计令牌 (Design Tokens)

### 10.1 阴影系统
```scss
$box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
                  0 6px 16px 0 rgba(0, 0, 0, 0.08),
                  0 9px 28px 8px rgba(0, 0, 0, 0.05);

$box-shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
                  0 1px 6px -1px rgba(0, 0, 0, 0.02),
                  0 2px 4px 0 rgba(0, 0, 0, 0.02);
```

### 10.2 圆角系统
```scss
$border-radius-base: 6px;   // 基础圆角
$border-radius-sm: 4px;     // 小圆角
$border-radius-lg: 8px;     // 大圆角
```

## 11. 页面布局设计规范

### 11.1 爬虫任务管理页面 (上下分割布局)

#### 爬虫任务管理页 (Crawler Task Management Page)
```
页面布局: 上下分割布局，上部分创建任务，下部分任务列表

┌─ 页面标题 ─────────────────────────────────────────────┐
│ 爬虫任务管理                                           │
└─────────────────────────────────────────────────────┘

┌─ 上部分：创建任务区域 ─────────────────────────────────┐
│ ┌─ 创建爬虫任务 ─────────────────────────────────────┐ │
│ │ 任务名称: [_______________] 目标平台: [Instagram▼] │ │
│ │ 关键词: [美妆,护肤,化妆品]  目标地区: [中国▼]      │ │
│ │ 粉丝范围: [1万] 至 [100万]  预期数量: [500]个KOL   │ │
│ │                                    [创建任务]     │ │
│ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘

┌─ 下部分：任务列表区域 ─────────────────────────────────┐
│ ┌─ 筛选区域 ─────────────────────────────────────────┐ │
│ │ 状态: [全部▼] 平台: [全部▼] 时间: [最近7天▼]       │ │
│ └─────────────────────────────────────────────────┘ │
│                                                       │
│ ┌─ 任务列表表格 ─────────────────────────────────────┐ │
│ │ 任务名称 | 平台 | 状态 | 进度 | 创建时间 | 操作     │ │
│ │ 美妆KOL  | IG   |运行中| 65% | 01-10   | 查看/停止│ │
│ │ 时尚博主 | YT   |已完成|100% | 01-09   | 查看/下载│ │
│ │ 旅行达人 | TK   |等待中| 0%  | 01-10   | 查看/删除│ │
│ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘

布局比例: 上部分占30%，下部分占70%
分割线: 使用细线分割，可拖拽调整比例
```

### 11.2 KOL主表管理页面

#### KOL列表页 (KOL List Page)
```
页面布局: 标准数据表格布局

┌─ 页面头部 ─────────────────────────────────────────────┐
│ KOL主表管理                    [导入] [导出] [+ 新增KOL] │
└─────────────────────────────────────────────────────┘

┌─ 搜索筛选区 ───────────────────────────────────────────┐
│ 🔍 [搜索KOL名称、账号...]                              │
│ 平台: [全部▼] 粉丝数: [全部▼] 国家: [全部▼] 状态: [全部▼] │
└─────────────────────────────────────────────────────┘

┌─ KOL数据表格 ──────────────────────────────────────────┐
│ ☑ | 头像 | 昵称 | 平台 | 粉丝数 | 国家 | 标签 | 状态 | 操作 │
│ ☑ | [👤] | 张小美| IG  | 125K  | 中国 | 美妆 | 活跃 | ⋯   │
│ ☑ | [👤] | 李小红| YT  | 89K   | 美国 | 时尚 | 活跃 | ⋯   │
└─────────────────────────────────────────────────────┘
```

#### KOL详情页 (KOL Detail Page)
```
页面布局: 标签页详情布局

┌─ 页面头部 ─────────────────────────────────────────────┐
│ ← 返回列表  KOL详情 - 张小美 (Instagram)    [编辑] [删除] │
└─────────────────────────────────────────────────────┘

┌─ 基础信息卡片 ─────────────────────────────────────────┐
│ [大头像] 张小美 (@zhangxiaomei)                        │
│ 平台: Instagram  粉丝: 125.6K  互动率: 3.2%            │
│ 国家: 中国  标签: [美妆] [护肤] [时尚]                  │
└─────────────────────────────────────────────────────┘

┌─ 详情标签页 ───────────────────────────────────────────┐
│ [基础信息] [数据统计] [内容作品] [合作记录] [备注日志]    │
│                                                       │
│ [当前选中标签页的内容区域]                              │
└─────────────────────────────────────────────────────┘
```

#### 编辑KOL弹窗/页面 (Edit KOL Modal/Page)
```
弹窗布局: 模态框表单，宽度800px

┌─ 编辑KOL信息 ──────────────────────────────────── × ┐
│                                                     │
│ ┌─ 基础信息 ─────────────────────────────────────┐ │
│ │ 昵称: [张小美_______________] 平台: [Instagram▼] │ │
│ │ 邮箱: [zhang@example.com____] 电话: [138****8888] │ │
│ │ 账号链接: [https://instagram.com/zhangxiaomei__] │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ ┌─ 数据信息 ─────────────────────────────────────┐ │
│ │ 粉丝数: [125600] 互动率: [3.2%] 发布频率: [3次/周] │ │
│ │ 合作状态: [合作中▼] 负责人: [李经理▼]            │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ ┌─ 标签管理 ─────────────────────────────────────┐ │
│ │ 当前标签: [美妆×] [护肤×] [时尚×]                │ │
│ │ 添加标签: [输入新标签_______] [添加]             │ │
│ │ 推荐标签: [生活方式] [奢侈品] [健身] [旅行]       │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ ┌─ 备注信息 ─────────────────────────────────────┐ │
│ │ [合作态度积极，内容质量高，适合长期合作...]       │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│                              [取消] [保存草稿] [保存] │
└─────────────────────────────────────────────────────┘

表单验证:
- 邮箱格式验证
- 粉丝数数值验证
- 必填字段检查
- 实时保存草稿功能
```

### 11.3 候选人管理页面

#### 候选人表格页 (Candidate Table Page)
```
页面布局: 数据管理表格布局

┌─ 页面头部 ─────────────────────────────────────────────┐
│ 候选人管理                        [批量导入] [+ 新增候选人] │
└─────────────────────────────────────────────────────┘

┌─ 批量操作区 ───────────────────────────────────────────┐
│ 已选择 3 项  [批量删除] [批量标记] [批量导出]            │
└─────────────────────────────────────────────────────┘

┌─ 候选人表格 ───────────────────────────────────────────┐
│ ☑ | 姓名 | 平台账号 | 粉丝数 | 邮箱 | 状态 | 添加时间 | 操作 │
│ ☑ | 王小白| @wangxb | 56K   | w@xx | 待联系| 01-10  | ⋯   │
│ ☑ | 李小绿| @lixl   | 78K   | l@xx | 已联系| 01-09  | ⋯   │
└─────────────────────────────────────────────────────┘
```

### 11.4 支付与数据追踪页面

#### 基础信息录入页 (Basic Info Entry Page)
```
页面布局: 表单录入布局

┌─ 页面标题 ─────────────────────────────────────────────┐
│ 支付与数据追踪 - 基础信息录入                           │
└─────────────────────────────────────────────────────┘

┌─ 录入表单 ─────────────────────────────────────────────┐
│ KOL ID:     [KOL_001] (自动生成或手动输入)              │
│ 社交链接:   [https://instagram.com/xxx]               │
│ 支付金额:   [¥ 5000.00]                               │
│ 支付方式:   [银行转账 ▼]                               │
│ 合作类型:   [品牌推广 ▼]                               │
│ 预期交付:   [2025-01-20] (日期选择器)                  │
│ 备注说明:   [_________________________]               │
│                                                       │
│                                    [重置] [保存记录]    │
└─────────────────────────────────────────────────────┘
```

#### 数据详情页 (Data Detail Page)
```
页面布局: 详情展示 + 编辑布局

┌─ 记录详情卡片 ─────────────────────────────────────────┐
│ KOL ID: KOL_001                              [编辑] [删除] │
│ 社交链接: https://instagram.com/zhangxiaomei           │
│ 支付金额: ¥5,000.00                                   │
│ 支付状态: [已支付] 支付时间: 2025-01-10 14:30          │
│ 合作状态: [进行中] 预期交付: 2025-01-20                │
└─────────────────────────────────────────────────────┘

┌─ 数据追踪表格 ─────────────────────────────────────────┐
│ 日期     | 曝光量 | 点击量 | 互动量 | 转化率 | 备注      │
│ 01-10   | 12.5K | 856   | 324   | 2.3%  | 首日数据   │
│ 01-11   | 15.2K | 1.2K  | 445   | 2.8%  | 数据上升   │
└─────────────────────────────────────────────────────┘
```

### 11.5 邮件记录页面

#### 邮件列表页 (Email List Page)
```
页面布局: 分组列表布局

┌─ 页面头部 ─────────────────────────────────────────────┐
│ 邮件记录管理                              [发送邮件] [模板管理] │
└─────────────────────────────────────────────────────┘

┌─ 筛选区域 ─────────────────────────────────────────────┐
│ KOL: [全部▼] 状态: [全部▼] 时间: [最近30天▼]            │
└─────────────────────────────────────────────────────┘

┌─ 邮件记录 (按KOL分组) ──────────────────────────────────┐
│ 📧 张小美 (<EMAIL>)                         │
│   ├─ 合作邀请邮件    01-10 14:30  ✅ 已发送  📖 已读    │
│   ├─ 跟进邮件       01-12 09:15  ✅ 已发送  📖 已读    │
│   └─ 合同确认       01-15 16:20  ⏳ 发送中  ⭕ 未读    │
│                                                       │
│ 📧 李小红 (<EMAIL>)                            │
│   ├─ 合作邀请邮件    01-09 11:20  ❌ 发送失败 ⭕ 未读   │
│   └─ 重发邮件       01-10 10:30  ✅ 已发送  📖 已读    │
└─────────────────────────────────────────────────────┘
```

### 11.6 KOL数据判定页面 (数据筛选与操作页面)

#### KOL数据判定页 (KOL Data Judgment Page)
```
页面布局: 类似KOL主表的表格布局，专门用于数据质量管理

┌─ 页面头部 ─────────────────────────────────────────────┐
│ KOL数据判定                           [刷新数据] [批量操作] │
└─────────────────────────────────────────────────────┘

┌─ 筛选条件区 ───────────────────────────────────────────┐
│ 🔍 [搜索KOL名称、账号...]                              │
│ 数据状态: [缺少邮箱▼] [未Mismatch▼] [全部▼]            │
│ 平台: [全部▼] 粉丝数: [全部▼] 国家: [全部▼]            │
└─────────────────────────────────────────────────────┘

┌─ 数据统计卡片 ─────────────────────────────────────────┐
│ [缺少邮箱: 156个] [待Mismatch: 89个] [待Nano解锁: 67个] │
└─────────────────────────────────────────────────────┘

┌─ KOL数据表格 ──────────────────────────────────────────┐
│ ☑ | 头像 | 昵称 | 平台 | 粉丝数 | 国家 | 邮箱状态 | 操作   │
│ ☑ | [👤] | 张小美| IG  | 125K  | 中国 | ❌ 缺少  |Mismatch│
│ ☑ | [👤] | 李小红| YT  | 89K   | 美国 | ✅ 已有  | Nano   │
│ ☑ | [👤] | 王小白| TK  | 156K  | 日本 | ❌ 缺少  |Mismatch│
│ ☑ | [👤] | 赵小绿| IG  | 234K  | 韩国 | ⚠️ 待验证 | Nano   │
└─────────────────────────────────────────────────────┘

┌─ 批量操作区 ───────────────────────────────────────────┐
│ 已选择 3 项  [批量Mismatch] [批量Nano解锁] [批量导出]   │
└─────────────────────────────────────────────────────┘

操作按钮说明:
- Mismatch按钮: 对选中的KOL进行邮箱匹配检查
- Nano按钮: 对选中的KOL进行Nano数据解锁
- 按钮状态: 根据KOL当前状态显示不同的可用操作
```

#### API操作状态设计
```scss
// KOL数据状态标识
.kol-data-status {
  // 邮箱状态
  &.email-missing { color: #f5222d; }     // ❌ 缺少邮箱
  &.email-exists { color: #52c41a; }      // ✅ 已有邮箱
  &.email-pending { color: #faad14; }     // ⚠️ 待验证

  // API操作按钮状态
  &.mismatch-available {
    background: #1890ff;
    color: white;
    border: none;
    cursor: pointer;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
  }
  &.mismatch-loading {
    background: #1890ff;
    color: white;
    cursor: not-allowed;
    opacity: 0.7;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    // 添加loading动画
    &::after {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid transparent;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 8px;
    }
  }
  &.nano-available {
    background: #722ed1;
    color: white;
    border: none;
    cursor: pointer;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
  }
  &.nano-loading {
    background: #722ed1;
    color: white;
    cursor: not-allowed;
    opacity: 0.7;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    &::after {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid transparent;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 8px;
    }
  }
  &.operation-disabled {
    background: #f5f5f5;
    color: #8c8c8c;
    cursor: not-allowed;
    border: 1px solid #d9d9d9;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
  }
  &.operation-success {
    background: #52c41a;
    color: white;
    cursor: default;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
  }
  &.operation-failed {
    background: #f5222d;
    color: white;
    cursor: pointer; // 允许重试
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    &:hover {
      background: #ff4d4f;
    }
  }
}

// Loading动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 批量操作进度条
.batch-operation-progress {
  width: 100%;
  height: 6px;
  background: #f5f5f5;
  border-radius: 3px;
  overflow: hidden;
  margin: 16px 0;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);

  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    transition: width 0.3s ease;
    border-radius: 3px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255,255,255,0.3),
        transparent
      );
      animation: shimmer 2s infinite;
    }
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// 批量操作统计
.batch-operation-stats {
  display: flex;
  gap: 16px;
  margin: 12px 0;
  font-size: 14px;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;

    &.success { color: #52c41a; }
    &.failed { color: #f5222d; }
    &.pending { color: #faad14; }
    &.total { color: #595959; }
  }
}
```

#### API调用反馈设计规范

##### 单个操作反馈
```
操作按钮状态变化流程:

初始状态: [Mismatch] / [Nano]
    ↓ (点击后)
加载状态: [Mismatch中...🔄] / [Nano解锁中...🔄]
    ↓ (API调用完成)
成功状态: [✅ 已完成]
失败状态: [❌ 失败，点击重试]
```

##### 批量操作反馈界面
```
┌─ 批量操作进度面板 ─────────────────────────────────────┐
│ 批量Mismatch操作进行中...                    [取消操作] │
│                                                       │
│ ████████████░░░░░░░░░░░░░░░░░░░░ 45% (9/20)           │
│                                                       │
│ ✅ 成功: 7个  ❌ 失败: 2个  ⏳ 处理中: 1个  ⏸️ 剩余: 10个 │
│                                                       │
│ 最近操作:                                             │
│ • 张小美 - ✅ 邮箱匹配成功                             │
│ • 李小红 - ❌ API调用失败，网络超时                    │
│ • 王小白 - 🔄 处理中...                               │
│                                                       │
│                           [暂停] [查看失败详情] [继续] │
└─────────────────────────────────────────────────────┘
```

##### 错误提示设计
```scss
// 错误提示样式
.api-error-message {
  padding: 12px 16px;
  border-radius: 6px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;

  &.network-error {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    color: #f5222d;
  }

  &.api-limit-error {
    background: #fffbe6;
    border: 1px solid #ffe58f;
    color: #faad14;
  }

  &.partial-failure {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
  }

  .error-icon {
    font-size: 16px;
  }

  .error-text {
    flex: 1;
    font-size: 14px;
  }

  .error-action {
    button {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
    }
  }
}
```

##### 错误提示文案规范
```
网络错误: "⚠️ 网络连接异常，请检查网络后重试"
API限流: "⚠️ 请求过于频繁，请稍后再试 (剩余配额: 50次)"
服务异常: "⚠️ 服务暂时不可用，请稍后重试"
数据异常: "⚠️ 数据格式错误，请检查输入内容"
部分失败: "⚠️ 批量操作部分失败: 成功 8个，失败 2个 [查看详情]"
权限不足: "⚠️ 权限不足，请联系管理员"
配额不足: "⚠️ API调用配额不足，请联系管理员充值"
```

##### 成功反馈设计
```scss
// 成功提示样式
.api-success-message {
  padding: 12px 16px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  color: #52c41a;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;

  .success-icon {
    font-size: 16px;
  }

  .success-text {
    font-size: 14px;
  }
}
```

##### 成功提示文案规范
```
单个成功: "✅ Mismatch完成，已获取邮箱信息"
单个成功: "✅ Nano解锁完成，数据已更新"
批量成功: "✅ 批量操作完成: 成功处理 15个 KOL"
部分成功: "✅ 批量操作完成: 成功 12个，失败 3个，可重试失败项"
```

## 12. 页面状态设计规范

### 12.1 爬虫任务状态标识
```scss
// 爬虫任务状态颜色
.crawler-task-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.pending {
    color: #faad14;
    background: #fffbe6;
    border: 1px solid #ffe58f;
  }    // 等待中
  &.running {
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
  }    // 运行中
  &.completed {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }  // 已完成
  &.failed {
    color: #f5222d;
    background: #fff2f0;
    border: 1px solid #ffccc7;
  }     // 失败
  &.paused {
    color: #8c8c8c;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
  }     // 已暂停
}

// 任务进度条
.task-progress {
  width: 100%;
  height: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;

  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;

    &.running {
      background: linear-gradient(90deg, #1890ff, #40a9ff);
    }
    &.completed {
      background: linear-gradient(90deg, #52c41a, #73d13d);
    }
    &.failed {
      background: linear-gradient(90deg, #f5222d, #ff4d4f);
    }
    &.paused {
      background: linear-gradient(90deg, #8c8c8c, #bfbfbf);
    }
  }
}
```

### 12.2 邮件状态标识
```scss
// 邮件状态图标和颜色
.email-status {
  &.sent { color: #52c41a; }      // ✅ 已发送
  &.sending { color: #1890ff; }   // ⏳ 发送中
  &.failed { color: #f5222d; }    // ❌ 发送失败
  &.read { color: #722ed1; }      // 📖 已读
  &.unread { color: #8c8c8c; }    // ⭕ 未读
}
```

### 12.3 候选人状态标识
```scss
// 候选人状态颜色
.candidate-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.pending {
    color: #8c8c8c;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
  }    // 待联系
  &.contacted {
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
  }  // 已联系
  &.replied {
    color: #722ed1;
    background: #f9f0ff;
    border: 1px solid #d3adf7;
  }    // 已回复
  &.rejected {
    color: #f5222d;
    background: #fff2f0;
    border: 1px solid #ffccc7;
  }   // 已拒绝
  &.converted {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }  // 已转化
}
```

### 12.4 支付状态标识
```scss
// 支付状态颜色
.payment-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.unpaid {
    color: #faad14;
    background: #fffbe6;
    border: 1px solid #ffe58f;
  }      // 未支付
  &.paid {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }        // 已支付
  &.partial {
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
  }     // 部分支付
  &.overdue {
    color: #f5222d;
    background: #fff2f0;
    border: 1px solid #ffccc7;
  }     // 逾期
  &.refunded {
    color: #8c8c8c;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
  }    // 已退款
}
```

### 12.5 数据判定结果标识
```scss
// 判定结果样式
.judgment-result {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.pass {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }
  &.fail {
    color: #f5222d;
    background: #fff2f0;
    border: 1px solid #ffccc7;
  }
}
```

## 13. 主题配置

### 13.1 Ant Design 主题定制
```tsx
import { ConfigProvider } from 'antd';

const theme = {
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 6,
    fontSize: 14,
  },
  components: {
    Button: {
      borderRadius: 6,
    },
    Input: {
      borderRadius: 6,
    },
    Table: {
      headerBg: '#fafafa',
      headerColor: '#262626',
    },
    Modal: {
      borderRadius: 8,
    },
  },
};

<ConfigProvider theme={theme}>
  <App />
</ConfigProvider>
```

---

## 14. 文档对齐检查清单

### 14.1 页面设计对齐状态

| 功能需求文档页面 | 设计系统文档对应设计 | 对齐状态 |
|-----------------|-------------------|---------|
| 4.1 爬虫任务管理页面 | 11.1 爬虫任务管理页面 | ✅ 已对齐 |
| 4.2.1 KOL列表页 | 11.2 KOL列表页 | ✅ 已对齐 |
| 4.2.2 编辑KOL弹窗 | 11.2 编辑KOL弹窗 | ✅ 已对齐 |
| 4.3 候选人管理页面 | 11.3 候选人管理页面 | ✅ 已对齐 |
| 4.4 支付与数据追踪页面 | 11.4 支付与数据追踪页面 | ✅ 已对齐 |
| 4.5 邮件记录页面 | 11.5 邮件记录页面 | ✅ 已对齐 |
| 4.6 KOL数据判定页面 | 11.6 KOL数据判定页面 | ✅ 已对齐 |

### 14.2 状态设计对齐状态

| 功能需求中的状态 | 设计系统中的样式 | 对齐状态 |
|----------------|----------------|---------|
| 爬虫任务状态 | 12.1 爬虫任务状态标识 | ✅ 已对齐 |
| 邮件状态 | 12.2 邮件状态标识 | ✅ 已对齐 |
| 候选人状态 | 12.3 候选人状态标识 | ✅ 已对齐 |
| 支付状态 | 12.4 支付状态标识 | ✅ 已对齐 |
| API操作状态 | 11.6 API操作状态设计 | ✅ 已对齐 |
| 数据判定结果 | 12.5 数据判定结果标识 | ✅ 已对齐 |

### 14.3 API调用功能对齐状态

| 功能需求中的API功能 | 设计系统中的设计 | 对齐状态 |
|-------------------|----------------|---------|
| Mismatch API调用 | API操作状态设计 | ✅ 已对齐 |
| Nano解锁API调用 | API操作状态设计 | ✅ 已对齐 |
| 批量API操作 | 批量操作进度条设计 | ✅ 已对齐 |
| API错误处理 | API调用反馈设计规范 | ✅ 已对齐 |
| 加载状态显示 | Loading动画设计 | ✅ 已对齐 |

### 14.4 交互反馈对齐状态

| 功能需求中的反馈 | 设计系统中的设计 | 对齐状态 |
|----------------|----------------|---------|
| 成功提示 | 成功反馈设计 | ✅ 已对齐 |
| 错误提示 | 错误提示设计 | ✅ 已对齐 |
| 进度反馈 | 批量操作进度面板 | ✅ 已对齐 |
| 状态变化 | 按钮状态变化流程 | ✅ 已对齐 |

### 14.5 对齐完成度总结

- **页面设计对齐度：** 100% (7/7)
- **状态设计对齐度：** 100% (6/6)
- **API功能对齐度：** 100% (5/5)
- **交互反馈对齐度：** 100% (4/4)

**总体对齐完成度：** ✅ 100% 完全对齐

---

**设计系统维护：** 设计规范如有更新，需同步更新此文档并通知设计和开发团队。
