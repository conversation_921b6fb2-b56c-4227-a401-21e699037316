# KOL-Hub 功能需求规格说明

**项目名称：** KOL-Hub 内部KOL管理平台  
**文档版本：** v1.0  
**最后更新：** 2025-01-10  
**目标受众：** 产品经理、前端开发人员、后端开发人员、测试人员

## 1. 文档概述

本文档专门定义 KOL-Hub 项目各个核心页面和组件的功能需求、用户故事、交互流程和验收标准。为产品开发和测试提供详细的功能规范。

## 2. 用户角色定义

### 2.1 主要用户角色
- **运营经理：** 查看整体业务数据，制定KOL合作策略
- **市场专员：** 管理KOL关系，执行具体合作活动
- **数据分析师：** 分析KOL表现数据，优化合作效果
- **系统管理员：** 管理用户权限，维护系统配置

## 3. 核心页面功能规格

### 3.1 登录页 (Login Page)

#### 用户故事
> 作为一个公司员工，我希望能通过我的企业邮箱和密码安全地登录KOL管理系统，以便开始我的工作。

#### 功能需求

**页面元素：**
- 公司Logo (居中显示)
- 系统标题 "KOL-Hub 管理平台"
- 用户名输入框 (邮箱格式验证)
- 密码输入框 (密码强度提示)
- "记住我" 复选框
- "登录" 主按钮
- "忘记密码" 链接

**交互流程：**
1. 用户输入邮箱和密码
2. 点击登录按钮
3. 前端验证输入格式
4. 发送登录请求到后端
5. 根据响应结果：
   - 成功：跳转到仪表板
   - 失败：显示错误信息

**验收标准：**
- ✅ 邮箱格式验证正确
- ✅ 密码不能为空
- ✅ 登录失败时显示明确错误信息
- ✅ 登录成功后正确跳转
- ✅ "记住我"功能正常工作

---

### 3.2 仪表板 (Dashboard)

#### 用户故事
> 作为一个运营经理，我希望一登录就能看到最重要的KPI指标和趋势图表，以便快速了解当前业务状况并做出决策。

#### 功能需求

**KPI卡片区 (4个核心指标)：**
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│  总KOL数    │ 本月新增KOL │ 进行中活动  │ 活动总曝光  │
│    1,234    │     +56     │     23      │   2.3M     │
│  (****%)    │  (+12.3%)   │   (-2.1%)   │  (****%)   │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

**图表区域：**
- **KOL趋势图：** 折线图显示过去6个月每月新增KOL数量
- **活动状态分布：** 饼图显示活动状态占比
  - 策划中 (Planning)
  - 进行中 (Active)  
  - 已完成 (Completed)
  - 已终止 (Cancelled)

**验收标准：**
- ✅ KPI数据实时更新
- ✅ 趋势图数据准确显示
- ✅ 饼图交互正常（悬停显示详情）
- ✅ 响应式布局适配不同屏幕

---

### 3.3 KOL列表页 (KOL List Page)

#### 用户故事
> 作为一个市场专员，我希望能快速搜索、筛选和排序我管理的KOL，以便找到符合特定活动要求的合作伙伴。

#### 功能需求

**搜索与筛选区：**
```
┌─ 搜索框 ─────────────────────────────────────────────┐
│ 🔍 搜索KOL名称、平台账号...                          │
└─────────────────────────────────────────────────────┘

┌─ 高级筛选 ─────────────────────────────────────────────┐
│ 平台: [全部▼] 粉丝数: [1万-10万▼] 标签: [美妆▼]      │
│ 状态: [合作中▼] 负责人: [张三▼]                       │
└─────────────────────────────────────────────────────┘
```

**数据表格：**
| 列名 | 宽度 | 排序 | 说明 |
|------|------|------|------|
| KOL信息 | 200px | ❌ | 头像+昵称+平台标识 |
| 平台 | 100px | ✅ | YouTube/Instagram/TikTok |
| 粉丝数 | 120px | ✅ | 格式化显示(1.2M) |
| 互动率 | 100px | ✅ | 百分比显示 |
| 标签 | 150px | ❌ | 标签组显示 |
| 状态 | 100px | ✅ | 合作状态 |
| 负责人 | 100px | ✅ | 员工姓名 |
| 操作 | 120px | ❌ | 查看/编辑/删除 |

**页面操作：**
- 右上角 "+ 新增KOL" 按钮
- 批量操作：批量导出、批量分配
- 分页控件：每页显示20/50/100条

**验收标准：**
- ✅ 搜索功能实时响应
- ✅ 筛选条件组合正确
- ✅ 表格排序功能正常
- ✅ 分页功能正确
- ✅ 批量操作功能完整

---

### 3.4 KOL详情页 (KOL Profile Page)

#### 用户故事
> 作为一个市场专员，我希望查看某个KOL的完整信息档案，包括基础资料、历史表现和合作记录，以便全面评估其合作价值。

#### 功能需求

**页面头部：**
```
← 返回列表    KOL详情 - 张小美 (Instagram)    [编辑] [删除]
```

**标签页结构：**

**Tab 1: 概览 (Overview)**
```
┌─ 基础信息卡片 ─────────────┬─ 数据概览卡片 ─────────────┐
│ 头像: [大头像]              │ 粉丝数: 125.6K             │
│ 昵称: 张小美                │ 互动率: 3.2%               │
│ 平台: Instagram            │ 平均点赞: 4.2K             │
│ 邮箱: <EMAIL>    │ 平均评论: 156              │
│ 电话: 138****8888          │ 发布频率: 3次/周           │
│ 标签: [美妆][护肤][时尚]    │ 合作次数: 8次              │
└───────────────────────────┴───────────────────────────┘

┌─ 粉丝增长趋势图 ─────────────────────────────────────────┐
│     [折线图显示最近6个月粉丝增长情况]                    │
└─────────────────────────────────────────────────────────┘
```

**Tab 2: 合作历史 (Campaign History)**
- 历史活动列表表格
- 活动名称、时间、状态、效果数据
- 支持按时间、状态筛选

**Tab 3: 内容作品 (Content Portfolio)**
- 代表作品网格展示
- 作品缩略图、发布时间、互动数据
- 支持预览和外链跳转

**Tab 4: 备注与日志 (Notes & Logs)**
- 沟通记录时间线
- 添加新备注功能
- 系统操作日志

**验收标准：**
- ✅ 标签页切换流畅
- ✅ 数据图表正确显示
- ✅ 历史记录完整准确
- ✅ 备注功能正常工作

---

### 3.5 KOL发现页 (KOL Discovery Page)

#### 用户故事
> 作为一个市场专员，我希望能通过统一的发现界面，选择不同的社交平台和数据源，使用精细化的筛选条件来搜索潜在的合作KOL，并能一键将他们导入到我们的管理系统中。

#### 功能需求

**步骤式引导流程：**

**步骤1: 选择社交平台**
```
┌─ 平台选择 ─────────────────────────────────────────────┐
│  [YouTube]    [Instagram]    [TikTok]                │
│   📺 视频      📷 图片        🎵 短视频               │
└─────────────────────────────────────────────────────┘
```

**步骤2: 选择数据源**
```
┌─ 数据源选择 (基于所选平台) ─────────────────────────────┐
│  ○ Modash API    ○ Social Blade    ○ ttone API      │
│    (推荐)          (免费额度)        (高精度)         │
└─────────────────────────────────────────────────────┘
```

**步骤3: 设置筛选条件**
```
┌─ 筛选条件表单 ─────────────────────────────────────────┐
│ 关键词: [美妆 护肤 化妆品]                             │
│ 粉丝数: [1万] 至 [100万]                              │
│ 互动率: [1%] 至 [10%]                                 │
│ 地区: [中国大陆▼]                                     │
│ 语言: [中文▼]                                         │
│ 性别: [不限▼]                                         │
│                                    [重置] [开始搜索]   │
└─────────────────────────────────────────────────────┘
```

**步骤4: 搜索结果展示**
```
┌─ KOL简报卡片网格 ─────────────────────────────────────┐
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│ │[头像] 张小美 │ │[头像] 李小红 │ │[头像] 王小白 │      │
│ │125K 粉丝    │ │89K 粉丝     │ │156K 粉丝    │      │
│ │3.2% 互动率  │ │4.1% 互动率  │ │2.8% 互动率  │      │
│ │[美妆][时尚] │ │[美食][生活] │ │[旅行][摄影] │      │
│ │[导入系统]   │ │[导入系统]   │ │[导入系统]   │      │
│ └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────┘
```

**交互流程：**
1. 用户选择社交平台
2. 根据平台显示可用数据源
3. 用户选择数据源并填写筛选条件
4. 点击搜索，显示加载状态
5. 展示搜索结果卡片
6. 用户可以预览KOL详情或直接导入

**验收标准：**
- ✅ 步骤引导清晰易懂
- ✅ 筛选条件验证正确
- ✅ 搜索结果准确展示
- ✅ 导入功能正常工作
- ✅ 错误处理友好提示

---

## 4. 新增核心页面功能规格

### 4.1 爬虫任务管理页面

#### 4.1.1 创建任务页 (Create Task Page)

**用户故事：**
> 作为一个市场专员，我希望能够创建自动化的KOL爬虫任务，通过设置平台、关键词、地区等参数来批量发现潜在的合作KOL。

**功能需求：**
- **任务基础信息：**
  - 任务名称（必填，最大50字符）
  - 目标平台（多选：Instagram、YouTube、TikTok）
  - 关键词设置（支持多个关键词，逗号分隔）
  - 目标地区（多选下拉框）
  - 粉丝数范围（滑块选择器）
  - 互动率范围（百分比输入）
  - 预期抓取数量
  - 备注说明（可选）

**交互流程：**
1. 用户填写任务基础信息
2. 设置筛选条件和参数
3. 预览任务配置
4. 提交创建任务
5. 跳转到任务列表页查看状态

**验收标准：**
- ✅ 表单验证完整准确
- ✅ 参数设置保存正确
- ✅ 任务创建成功后正确跳转
- ✅ 错误处理友好提示

#### 4.1.2 任务列表页 (Task List Page)

**用户故事：**
> 作为一个市场专员，我希望能够查看所有爬虫任务的执行状态和进度，以便及时了解任务完成情况并进行必要的管理操作。

**功能需求：**
- **任务状态管理：**
  - 状态显示：等待中、运行中、已完成、失败、已暂停
  - 进度条显示（百分比）
  - 实时状态更新
- **任务操作：**
  - 查看任务详情
  - 暂停/恢复任务
  - 停止任务
  - 删除任务
  - 下载结果
- **筛选和搜索：**
  - 按状态筛选
  - 按平台筛选
  - 按创建时间筛选
  - 任务名称搜索

**验收标准：**
- ✅ 任务状态实时更新
- ✅ 进度显示准确
- ✅ 操作功能正常
- ✅ 筛选搜索有效

### 4.2 KOL主表管理页面

#### 4.2.1 KOL列表页 (Enhanced KOL List Page)

**用户故事：**
> 作为一个市场专员，我希望能够管理完整的KOL主数据库，支持多维度筛选和批量操作，以便高效管理大量KOL资源。

**功能需求：**
- **增强筛选功能：**
  - 平台筛选（Instagram、YouTube、TikTok）
  - 粉丝数范围筛选
  - 国家/地区筛选
  - 标签筛选（多选）
  - 合作状态筛选
  - 最后更新时间筛选
- **批量操作：**
  - 批量导出KOL数据
  - 批量分配负责人
  - 批量修改标签
  - 批量删除
- **数据管理：**
  - 导入KOL数据（Excel/CSV）
  - 数据去重检测
  - 数据质量检查

**验收标准：**
- ✅ 筛选条件组合正确
- ✅ 批量操作功能完整
- ✅ 数据导入导出正常
- ✅ 性能优化（大数据量处理）

#### 4.2.2 编辑KOL弹窗/页面 (Edit KOL Modal/Page)

**用户故事：**
> 作为一个市场专员，我希望能够快速编辑KOL的基础信息和标签，保持数据的准确性和时效性。

**功能需求：**
- **可编辑字段：**
  - 基础信息（昵称、邮箱、电话）
  - 平台信息（账号链接、粉丝数、互动率）
  - 分类标签（支持新增、删除标签）
  - 合作状态和备注
- **数据验证：**
  - 邮箱格式验证
  - 粉丝数数值验证
  - 必填字段检查
- **保存机制：**
  - 实时保存草稿
  - 批量保存修改
  - 修改历史记录

**验收标准：**
- ✅ 字段编辑功能正常
- ✅ 数据验证准确
- ✅ 保存机制可靠
- ✅ 修改记录完整

### 4.3 候选人管理页面

#### 4.3.1 候选人表格页 (Candidate Table Page)

**用户故事：**
> 作为一个市场专员，我希望能够管理潜在的KOL候选人，跟踪联系状态和转化情况，以便提高KOL招募效率。

**功能需求：**
- **候选人状态管理：**
  - 状态标记：待联系、已联系、已回复、已拒绝、已转化
  - 状态批量修改
  - 状态变更历史
- **联系信息管理：**
  - 联系方式记录
  - 联系历史追踪
  - 下次联系提醒
- **批量操作：**
  - 批量导入候选人
  - 批量发送邮件
  - 批量转为正式KOL
  - 批量删除

**验收标准：**
- ✅ 状态管理功能完整
- ✅ 联系记录准确
- ✅ 批量操作有效
- ✅ 数据统计正确

### 4.4 支付与数据追踪页面

#### 4.4.1 基础信息录入页 (Basic Info Entry Page)

**用户故事：**
> 作为一个财务专员，我希望能够录入KOL的支付信息和合作链接，建立完整的财务追踪记录。

**功能需求：**
- **支付信息录入：**
  - KOL ID关联
  - 支付金额和方式
  - 合作类型和周期
  - 预期交付时间
- **链接管理：**
  - 社交媒体链接
  - 合作内容链接
  - 链接有效性检查
- **数据关联：**
  - 与KOL主表关联
  - 与合作活动关联
  - 支付状态同步

**验收标准：**
- ✅ 信息录入准确
- ✅ 数据关联正确
- ✅ 状态同步及时
- ✅ 权限控制严格

#### 4.4.2 数据详情页 (Data Detail Page)

**用户故事：**
> 作为一个数据分析师，我希望能够查看和编辑KOL合作的详细数据，追踪投资回报率和效果指标。

**功能需求：**
- **数据展示：**
  - 基础合作信息
  - 支付状态和金额
  - 效果数据追踪
  - ROI计算显示
- **数据编辑：**
  - 支持字段修改
  - 数据导入更新
  - 批量数据处理
- **数据分析：**
  - 趋势图表展示
  - 对比分析功能
  - 数据导出报告

**验收标准：**
- ✅ 数据展示完整
- ✅ 编辑功能正常
- ✅ 分析图表准确
- ✅ 报告导出有效

### 4.5 邮件记录页面

#### 4.5.1 邮件列表页 (Email List Page)

**用户故事：**
> 作为一个市场专员，我希望能够查看所有与KOL的邮件往来记录，跟踪沟通状态和回复情况。

**功能需求：**
- **邮件记录展示：**
  - 按KOL分组显示
  - 邮件发送状态标识
  - 邮件读取状态显示
  - 时间线展示
- **邮件管理：**
  - 邮件模板管理
  - 批量发送邮件
  - 邮件跟进提醒
  - 回复状态追踪
- **筛选和搜索：**
  - 按KOL筛选
  - 按状态筛选
  - 按时间范围筛选
  - 邮件内容搜索

**验收标准：**
- ✅ 邮件记录完整
- ✅ 状态显示准确
- ✅ 筛选功能有效
- ✅ 发送功能正常

### 4.6 KOL数据判定页面

#### 4.6.1 判定弹窗/结果表格 (Judgment Modal/Result Table)

**用户故事：**
> 作为一个市场专员，我希望能够批量输入候选KOL信息，通过预设规则快速判定哪些KOL符合我们的合作标准。

**功能需求：**
- **批量输入功能：**
  - 支持文本批量粘贴
  - 支持Excel文件导入
  - 数据格式自动识别
  - 输入数据预览
- **判定规则设置：**
  - 粉丝数范围设定
  - 地区要求设置
  - 标签匹配规则
  - 互动率阈值
- **结果展示：**
  - 判定结果表格
  - 符合/不符合原因
  - 结果统计汇总
  - 结果数据导出

**交互流程：**
1. 用户输入候选人数据
2. 设置判定规则
3. 执行批量判定
4. 查看判定结果
5. 导出符合条件的KOL

**验收标准：**
- ✅ 批量输入功能正常
- ✅ 判定规则准确执行
- ✅ 结果展示清晰
- ✅ 数据导出完整

---

## 5. 通用功能需求

### 5.1 权限控制
- 基于角色的访问控制 (RBAC)
- 页面级权限验证
- 操作级权限控制
- 数据级权限隔离

### 5.2 数据导出
- 支持Excel格式导出
- 自定义导出字段
- 批量导出功能
- 导出任务队列管理

### 5.3 操作日志
- 关键操作记录
- 操作时间和用户追踪
- 日志查询和筛选
- 数据变更历史

### 5.4 消息通知
- 系统消息推送
- 邮件通知集成
- 消息中心管理
- 任务状态通知

---

## 6. 非功能性需求

### 6.1 性能要求
- 页面加载时间 < 3秒
- 列表查询响应时间 < 1秒
- 支持1000+并发用户
- 大数据量处理优化

### 6.2 可用性要求
- 系统可用性 ≥ 99.5%
- 7x24小时服务
- 故障恢复时间 < 30分钟
- 数据备份策略

### 6.3 安全要求
- HTTPS加密传输
- 用户会话管理
- 数据备份和恢复
- 敏感数据加密存储

---

**需求变更管理：** 功能需求如有变更，需通过产品评审流程，并及时更新此文档。
