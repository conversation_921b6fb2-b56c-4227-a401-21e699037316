# KOL-Hub 技术架构与选型规格说明

**项目名称：** KOL-Hub 内部KOL管理平台  
**文档版本：** v1.0  
**最后更新：** 2025-01-10  
**目标受众：** 前端开发团队、架构师、技术负责人

## 1. 文档概述

本文档专门定义 KOL-Hub 项目的前端技术栈选型、架构模式和开发构建工具链。所有技术决策都基于企业级应用的稳定性、可维护性和团队协作效率考虑。

## 2. 核心技术栈

### 2.1 前端框架与库

#### React v18+ (强制)
- **选择理由：** 生态成熟，性能优异，企业级应用首选
- **版本要求：** >= 18.0.0
- **关键特性：** 并发特性、自动批处理、Suspense

#### React Router v6 (强制)
- **选择理由：** React 官方推荐路由解决方案
- **版本要求：** >= 6.0.0
- **核心功能：** 声明式路由、嵌套路由、数据加载

#### Redux Toolkit (RTK) (强制)
- **选择理由：** Redux 官方推荐工具集，简化状态管理
- **版本要求：** >= 1.9.0
- **Store 架构规划：**
  ```
  store/
  ├── slices/
  │   ├── userSlice.ts      # 用户信息、权限管理（模拟用户系统）
  │   ├── kolsSlice.ts      # KOL列表、详情、筛选
  │   ├── campaignsSlice.ts # 活动管理、状态跟踪
  │   ├── discoverySlice.ts # KOL发现、搜索结果
  │   └── uiSlice.ts        # 全局UI状态
  ├── middleware/
  └── index.ts
  ```

#### 简化用户系统设计
```typescript
// types/user.ts
export interface SimpleUser {
  id: string;
  username: string;
  email: string;
  profile: {
    name: string;
    avatar?: string;
  };
  apiQuota: {
    mismatch: { used: number; total: number };
    nano: { used: number; total: number };
  };
}

// 预定义测试账号（简化版）
export const mockUsers: Record<string, SimpleUser> = {
  '<EMAIL>': {
    id: 'user_001',
    username: 'testuser',
    email: '<EMAIL>',
    profile: {
      name: '测试用户'
    },
    apiQuota: {
      mismatch: { used: 150, total: 1000 },
      nano: { used: 80, total: 500 }
    }
  }
};
```

#### TypeScript (强制)
- **选择理由：** 类型安全，减少运行时错误，提升代码质量
- **版本要求：** >= 4.9.0
- **配置要求：** 严格模式开启，所有新代码必须符合TS规范

### 2.2 UI框架与样式

#### Ant Design v5 (强制)
- **选择理由：** 企业级UI组件库，设计规范统一
- **版本要求：** >= 5.0.0
- **主题配置：** 使用 ConfigProvider 进行全局主题定制

#### Styled-components (强制)
- **选择理由：** CSS-in-JS，组件级样式封装
- **版本要求：** >= 5.3.0
- **使用场景：** 自定义样式、主题扩展、动态样式

### 2.3 数据请求与API管理

#### Axios
- **选择理由：** 功能完善，拦截器支持，Promise基础
- **版本要求：** >= 1.0.0
- **封装要求：** 统一API服务层，请求/响应拦截器

#### API调用架构设计
```typescript
// API服务层结构
services/
├── api/
│   ├── base.ts           # 基础API配置
│   ├── kolApi.ts         # KOL相关API
│   ├── mismatchApi.ts    # Mismatch API调用
│   └── nanoApi.ts        # Nano解锁API
├── interceptors/
│   ├── request.ts        # 请求拦截器
│   ├── response.ts       # 响应拦截器
│   └── error.ts          # 错误处理
└── utils/
    ├── retry.ts          # 重试机制
    ├── queue.ts          # 请求队列管理
    └── cache.ts          # 响应缓存
```

#### API调用特殊要求
- **重试机制：** 网络失败自动重试，最多3次
- **请求队列：** 批量操作使用队列控制并发数
- **错误处理：** 统一错误码处理和用户友好提示
- **加载状态：** 全局loading状态管理
- **缓存策略：** 适当缓存减少重复请求

## 3. 开发工具链

### 3.1 构建工具

#### Vite (推荐)
- **选择理由：** 极速开发体验，HMR支持
- **版本要求：** >= 4.0.0
- **配置要求：** TypeScript、ESLint、Prettier集成

#### Yarn (包管理器)
- **选择理由：** 依赖管理稳定，lockfile机制
- **版本要求：** >= 1.22.0

### 3.2 代码质量工具

#### ESLint + Prettier (强制)
- **ESLint配置：**
  - `eslint-config-airbnb`
  - `eslint-plugin-react-hooks`
  - `@typescript-eslint/eslint-plugin`
- **Prettier配置：** 统一代码格式化规则
- **强制要求：** 所有代码必须通过ESLint检查

### 3.3 测试框架

#### Vitest + React Testing Library (强制)
- **单元测试：** 核心业务逻辑必须覆盖
- **组件测试：** 关键UI组件必须测试
- **覆盖率要求：** 核心模块 >= 80%

## 4. 项目结构规范

```
src/
├── components/          # 通用组件
│   ├── common/         # 基础组件
│   └── business/       # 业务组件
├── pages/              # 页面组件
├── store/              # Redux store
├── services/           # API服务层
│   ├── api/           # API接口定义
│   ├── interceptors/  # 请求拦截器
│   └── utils/         # API工具函数
├── hooks/              # 自定义Hooks
│   ├── useApi.ts      # API调用Hook
│   ├── useBatch.ts    # 批量操作Hook
│   └── useRetry.ts    # 重试机制Hook
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
├── constants/          # 常量定义
└── styles/             # 全局样式
```

## 5. 性能与优化要求

### 5.1 代码分割
- 路由级别的懒加载
- 组件级别的动态导入
- 第三方库的按需加载

### 6.2 构建优化
- Tree shaking 启用
- 代码压缩与混淆
- 静态资源优化

## 7. 兼容性要求

### 7.1 浏览器支持
- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

### 7.2 Node.js 环境
- Node.js >= 16.0.0
- npm >= 8.0.0

## 8. 技术选型总结

以下技术选型为**强制要求**，必须严格遵循：

1. ✅ React v18+ + TypeScript
2. ✅ React Router v6
3. ✅ Redux Toolkit (RTK)
4. ✅ Ant Design v5 + Styled-components
5. ✅ ESLint + Prettier 代码规范
6. ✅ Vite 构建工具
7. ✅ Vitest + React Testing Library 测试框架

---

**文档维护：** 技术架构如有变更，需更新此文档并通知相关开发人员。
