# KOL-Hub 技术架构与选型规格说明

**项目名称：** KOL-Hub 内部KOL管理平台  
**文档版本：** v1.0  
**最后更新：** 2025-01-10  
**目标受众：** 前端开发团队、架构师、技术负责人

## 1. 文档概述

本文档专门定义 KOL-Hub 项目的前端技术栈选型、架构模式和开发构建工具链。所有技术决策都基于企业级应用的稳定性、可维护性和团队协作效率考虑。

## 2. 核心技术栈

### 2.1 前端框架与库

#### React v18+ (强制)
- **选择理由：** 生态成熟，性能优异，企业级应用首选
- **版本要求：** >= 18.0.0
- **关键特性：** 并发特性、自动批处理、Suspense

#### React Router v6 (强制)
- **选择理由：** React 官方推荐路由解决方案
- **版本要求：** >= 6.0.0
- **核心功能：** 声明式路由、嵌套路由、数据加载

#### Redux Toolkit (RTK) (强制)
- **选择理由：** Redux 官方推荐工具集，简化状态管理
- **版本要求：** >= 1.9.0
- **Store 架构规划：**
  ```
  store/
  ├── slices/
  │   ├── userSlice.ts      # 用户信息、权限管理（模拟用户系统）
  │   ├── kolsSlice.ts      # KOL列表、详情、筛选
  │   ├── campaignsSlice.ts # 活动管理、状态跟踪
  │   ├── discoverySlice.ts # KOL发现、搜索结果
  │   └── uiSlice.ts        # 全局UI状态
  ├── middleware/
  └── index.ts
  ```

#### 简化用户系统设计
```typescript
// types/user.ts
export interface SimpleUser {
  id: string;
  username: string;
  email: string;
  profile: {
    name: string;
    avatar?: string;
  };
  apiQuota: {
    mismatch: { used: number; total: number };
    nano: { used: number; total: number };
  };
}

// 预定义测试账号（简化版）
export const mockUsers: Record<string, SimpleUser> = {
  '<EMAIL>': {
    id: 'user_001',
    username: 'testuser',
    email: '<EMAIL>',
    profile: {
      name: '测试用户'
    },
    apiQuota: {
      mismatch: { used: 150, total: 1000 },
      nano: { used: 80, total: 500 }
    }
  }
};
```

#### TypeScript (强制)
- **选择理由：** 类型安全，减少运行时错误，提升代码质量
- **版本要求：** >= 4.9.0
- **配置要求：** 严格模式开启，所有新代码必须符合TS规范

### 2.2 UI框架与样式

#### Ant Design v5 (强制)
- **选择理由：** 企业级UI组件库，设计规范统一
- **版本要求：** >= 5.0.0
- **主题配置：** 使用 ConfigProvider 进行全局主题定制

#### Styled-components (强制)
- **选择理由：** CSS-in-JS，组件级样式封装
- **版本要求：** >= 5.3.0
- **使用场景：** 自定义样式、主题扩展、动态样式

### 2.3 数据请求与API管理

#### Axios
- **选择理由：** 功能完善，拦截器支持，Promise基础
- **版本要求：** >= 1.0.0
- **封装要求：** 统一API服务层，请求/响应拦截器

#### API调用架构设计
```typescript
// API服务层结构
services/
├── api/
│   ├── base.ts           # 基础API配置
│   ├── kolApi.ts         # KOL相关API
│   ├── mismatchApi.ts    # Mismatch API调用
│   └── nanoApi.ts        # Nano解锁API
├── interceptors/
│   ├── request.ts        # 请求拦截器
│   ├── response.ts       # 响应拦截器
│   └── error.ts          # 错误处理
└── utils/
    ├── retry.ts          # 重试机制
    ├── queue.ts          # 请求队列管理
    └── cache.ts          # 响应缓存
```

#### API调用特殊要求
- **重试机制：** 网络失败自动重试，最多3次
- **请求队列：** 批量操作使用队列控制并发数
- **错误处理：** 统一错误码处理和用户友好提示
- **加载状态：** 全局loading状态管理
- **缓存策略：** 适当缓存减少重复请求

## 3. 开发工具链

### 3.1 构建工具

#### Vite (推荐)
- **选择理由：** 极速开发体验，HMR支持
- **版本要求：** >= 4.0.0
- **配置要求：** TypeScript、ESLint、Prettier集成

#### Yarn (包管理器)
- **选择理由：** 依赖管理稳定，lockfile机制
- **版本要求：** >= 1.22.0

### 3.2 代码质量工具

#### ESLint + Prettier (强制)
- **ESLint配置：**
  - `eslint-config-airbnb`
  - `eslint-plugin-react-hooks`
  - `@typescript-eslint/eslint-plugin`
- **Prettier配置：** 统一代码格式化规则
- **强制要求：** 所有代码必须通过ESLint检查

### 3.3 测试框架

#### Vitest + React Testing Library (强制)
- **单元测试：** 核心业务逻辑必须覆盖
- **组件测试：** 关键UI组件必须测试
- **覆盖率要求：** 核心模块 >= 80%

## 4. 项目结构规范

### 4.1 整体项目结构

```
kol-hub/
├── public/                     # 静态资源
│   ├── favicon.ico
│   ├── logo.png
│   └── index.html
├── src/                        # 源代码目录
│   ├── components/             # 组件目录
│   ├── pages/                  # 页面目录
│   ├── store/                  # 状态管理
│   ├── services/               # API服务层
│   ├── hooks/                  # 自定义Hooks
│   ├── utils/                  # 工具函数
│   ├── types/                  # TypeScript类型定义
│   ├── constants/              # 常量定义
│   ├── styles/                 # 样式文件
│   ├── assets/                 # 静态资源
│   ├── App.tsx                 # 根组件
│   ├── main.tsx                # 应用入口
│   └── vite-env.d.ts          # Vite类型声明
├── docs/                       # 项目文档
├── tests/                      # 测试文件
├── .env.example               # 环境变量示例
├── .gitignore                 # Git忽略文件
├── package.json               # 项目配置
├── tsconfig.json              # TypeScript配置
├── vite.config.ts             # Vite配置
├── eslint.config.js           # ESLint配置
└── README.md                  # 项目说明
```

### 4.2 详细目录结构

#### 4.2.1 组件目录 (src/components/)
```
components/
├── common/                     # 通用基础组件
│   ├── Button/                 # 按钮组件
│   │   ├── index.tsx
│   │   ├── Button.styled.ts
│   │   └── Button.types.ts
│   ├── Table/                  # 表格组件
│   │   ├── index.tsx
│   │   ├── Table.styled.ts
│   │   └── Table.types.ts
│   ├── Modal/                  # 模态框组件
│   ├── Form/                   # 表单组件
│   ├── Loading/                # 加载组件
│   └── index.ts               # 统一导出
├── business/                   # 业务组件
│   ├── KolCard/               # KOL卡片组件
│   │   ├── index.tsx
│   │   ├── KolCard.styled.ts
│   │   └── KolCard.types.ts
│   ├── ApiOperationButton/     # API操作按钮
│   │   ├── index.tsx
│   │   ├── ApiOperationButton.styled.ts
│   │   └── ApiOperationButton.types.ts
│   ├── BatchOperationPanel/    # 批量操作面板
│   ├── StatusIndicator/        # 状态指示器
│   ├── QuotaDisplay/          # 配额显示
│   └── index.ts               # 统一导出
├── layout/                     # 布局组件
│   ├── Header/                 # 顶部导航
│   ├── Sidebar/                # 侧边栏
│   ├── MainLayout/             # 主布局
│   └── index.ts
└── index.ts                   # 全局组件导出
```

#### 4.2.2 页面目录 (src/pages/)
```
pages/
├── auth/                       # 认证相关页面
│   ├── Login/
│   │   ├── index.tsx
│   │   ├── Login.styled.ts
│   │   └── Login.types.ts
│   └── index.ts
├── dashboard/                  # 仪表板
│   ├── index.tsx
│   ├── Dashboard.styled.ts
│   ├── components/             # 页面专用组件
│   │   ├── KpiCards/
│   │   ├── TrendChart/
│   │   └── StatusChart/
│   └── Dashboard.types.ts
├── crawler-management/         # 爬虫任务管理
│   ├── index.tsx
│   ├── CrawlerManagement.styled.ts
│   ├── components/
│   │   ├── TaskCreationForm/   # 任务创建表单
│   │   ├── TaskList/           # 任务列表
│   │   └── TaskStatusCard/     # 任务状态卡片
│   └── CrawlerManagement.types.ts
├── kol-management/             # KOL管理
│   ├── KolList/               # KOL列表页
│   │   ├── index.tsx
│   │   ├── KolList.styled.ts
│   │   ├── components/
│   │   │   ├── KolTable/
│   │   │   ├── FilterPanel/
│   │   │   └── BatchActions/
│   │   └── KolList.types.ts
│   ├── KolDetail/             # KOL详情页
│   │   ├── index.tsx
│   │   ├── KolDetail.styled.ts
│   │   ├── components/
│   │   │   ├── BasicInfo/
│   │   │   ├── DataStats/
│   │   │   ├── CampaignHistory/
│   │   │   └── NotesLog/
│   │   └── KolDetail.types.ts
│   ├── EditKolModal/          # 编辑KOL弹窗
│   └── index.ts
├── candidate-management/       # 候选人管理
│   ├── index.tsx
│   ├── CandidateManagement.styled.ts
│   ├── components/
│   │   ├── CandidateTable/
│   │   ├── CandidateForm/
│   │   └── StatusManager/
│   └── CandidateManagement.types.ts
├── data-tracking/              # 数据追踪
│   ├── PaymentEntry/          # 支付信息录入
│   ├── PaymentDetail/         # 支付详情
│   ├── DataTracking/          # 数据追踪
│   └── index.ts
├── email-management/           # 邮件管理
│   ├── EmailList/             # 邮件列表
│   ├── EmailTemplates/        # 邮件模板
│   └── index.ts
├── data-judgment/              # 数据判定
│   ├── index.tsx
│   ├── DataJudgment.styled.ts
│   ├── components/
│   │   ├── DataTable/
│   │   ├── OperationPanel/
│   │   └── ProgressTracker/
│   └── DataJudgment.types.ts
├── kol-discovery/              # KOL发现
│   ├── index.tsx
│   ├── components/
│   │   ├── PlatformSelector/
│   │   ├── FilterForm/
│   │   └── ResultGrid/
│   └── KolDiscovery.types.ts
└── index.ts                   # 页面路由导出
```

#### 4.2.3 状态管理目录 (src/store/)
```
store/
├── slices/                     # Redux Toolkit Slices
│   ├── userSlice.ts           # 用户信息管理
│   ├── crawlerSlice.ts        # 爬虫任务状态
│   ├── kolsSlice.ts           # KOL数据管理
│   ├── candidateSlice.ts      # 候选人管理
│   ├── dataTrackingSlice.ts   # 数据追踪
│   ├── emailSlice.ts          # 邮件记录
│   ├── dataJudgmentSlice.ts   # 数据判定
│   ├── discoverySlice.ts      # KOL发现
│   └── uiSlice.ts             # UI状态管理
├── middleware/                 # 中间件
│   ├── apiMiddleware.ts       # API调用中间件
│   └── loggerMiddleware.ts    # 日志中间件
├── selectors/                  # 选择器
│   ├── userSelectors.ts
│   ├── kolSelectors.ts
│   └── index.ts
├── types/                      # Store相关类型
│   ├── RootState.ts
│   └── AppDispatch.ts
└── index.ts                   # Store配置
```

#### 4.2.4 API服务层目录 (src/services/)
```
services/
├── api/                        # API接口定义
│   ├── base.ts                # 基础API配置
│   ├── auth.ts                # 认证相关API
│   ├── crawler.ts             # 爬虫任务API
│   ├── kol.ts                 # KOL管理API
│   ├── candidate.ts           # 候选人API
│   ├── dataTracking.ts        # 数据追踪API
│   ├── email.ts               # 邮件API
│   ├── mismatch.ts            # Mismatch API
│   ├── nano.ts                # Nano API
│   └── discovery.ts           # KOL发现API
├── interceptors/               # 请求拦截器
│   ├── request.ts             # 请求拦截器
│   ├── response.ts            # 响应拦截器
│   └── error.ts               # 错误处理
├── utils/                      # API工具函数
│   ├── retry.ts               # 重试机制
│   ├── queue.ts               # 请求队列
│   ├── cache.ts               # 缓存管理
│   └── transform.ts           # 数据转换
└── index.ts                   # 服务层统一导出
```

#### 4.2.5 其他目录结构
```
hooks/                          # 自定义Hooks
├── api/                        # API相关Hooks
│   ├── useApiCall.ts          # 通用API调用Hook
│   ├── useMismatchApi.ts      # Mismatch API Hook
│   ├── useNanoApi.ts          # Nano API Hook
│   └── useBatchOperation.ts   # 批量操作Hook
├── business/                   # 业务逻辑Hooks
│   ├── useKolManagement.ts    # KOL管理Hook
│   ├── useCrawlerTask.ts      # 爬虫任务Hook
│   └── useDataJudgment.ts     # 数据判定Hook
├── common/                     # 通用Hooks
│   ├── useLocalStorage.ts     # 本地存储Hook
│   ├── useDebounce.ts         # 防抖Hook
│   └── usePermission.ts       # 权限Hook
└── index.ts                   # Hooks统一导出

types/                          # TypeScript类型定义
├── api/                        # API相关类型
│   ├── common.ts              # 通用API类型
│   ├── kol.ts                 # KOL相关类型
│   ├── crawler.ts             # 爬虫任务类型
│   ├── candidate.ts           # 候选人类型
│   ├── dataTracking.ts        # 数据追踪类型
│   └── email.ts               # 邮件类型
├── components/                 # 组件Props类型
│   ├── common.ts              # 通用组件类型
│   └── business.ts            # 业务组件类型
├── pages/                      # 页面相关类型
├── store/                      # Store相关类型
├── user.ts                     # 用户类型
├── global.ts                   # 全局类型
└── index.ts                   # 类型统一导出

utils/                          # 工具函数
├── format/                     # 格式化工具
│   ├── date.ts                # 日期格式化
│   ├── number.ts              # 数字格式化
│   └── text.ts                # 文本处理
├── validation/                 # 验证工具
│   ├── email.ts               # 邮箱验证
│   ├── form.ts                # 表单验证
│   └── api.ts                 # API数据验证
├── storage/                    # 存储工具
│   ├── localStorage.ts        # 本地存储
│   └── sessionStorage.ts     # 会话存储
├── helpers/                    # 辅助函数
│   ├── array.ts               # 数组操作
│   ├── object.ts              # 对象操作
│   └── string.ts              # 字符串操作
└── index.ts                   # 工具函数导出

constants/                      # 常量定义
├── api.ts                      # API相关常量
├── routes.ts                   # 路由常量
├── status.ts                   # 状态常量
├── config.ts                   # 配置常量
└── index.ts                   # 常量统一导出

styles/                         # 样式文件
├── globals.css                 # 全局样式
├── variables.css               # CSS变量
├── themes/                     # 主题文件
│   ├── light.ts               # 浅色主题
│   └── dark.ts                # 深色主题
└── components/                 # 组件样式
    ├── antd-overrides.css     # Ant Design样式覆盖
    └── custom.css             # 自定义样式

assets/                         # 静态资源
├── images/                     # 图片资源
│   ├── logo/
│   ├── icons/
│   └── illustrations/
├── fonts/                      # 字体文件
└── data/                       # 静态数据文件
    ├── mock/                  # Mock数据
    └── config/                # 配置文件
```

### 4.3 文件命名规范

#### 4.3.1 文件夹命名
- **使用 kebab-case**：`kol-management`, `crawler-task`, `api-operation`
- **语义化命名**：文件夹名称应清晰表达其功能用途
- **复数形式**：集合类文件夹使用复数形式 (`components`, `pages`, `hooks`)

#### 4.3.2 文件命名
- **组件文件**：使用 PascalCase，如 `KolCard.tsx`, `ApiOperationButton.tsx`
- **Hook文件**：使用 camelCase，以 `use` 开头，如 `useApiCall.ts`, `useKolManagement.ts`
- **工具函数**：使用 camelCase，如 `formatDate.ts`, `validateEmail.ts`
- **类型文件**：使用 camelCase，如 `kolTypes.ts`, `apiTypes.ts`
- **样式文件**：组件样式使用 `ComponentName.styled.ts`

#### 4.3.3 组件文件组织
```
ComponentName/
├── index.tsx                   # 主组件文件
├── ComponentName.styled.ts     # 样式文件
├── ComponentName.types.ts      # 类型定义
├── ComponentName.test.tsx      # 测试文件
└── components/                 # 子组件（如果有）
    └── SubComponent/
```

#### 4.3.4 导出规范
- **统一导出**：每个文件夹都应有 `index.ts` 文件进行统一导出
- **命名导出**：优先使用命名导出，避免默认导出
- **类型导出**：类型定义使用 `export type` 进行导出

```typescript
// 示例：components/business/index.ts
export { KolCard } from './KolCard';
export { ApiOperationButton } from './ApiOperationButton';
export { BatchOperationPanel } from './BatchOperationPanel';

// 类型导出
export type { KolCardProps } from './KolCard/KolCard.types';
export type { ApiOperationButtonProps } from './ApiOperationButton/ApiOperationButton.types';
```

### 4.4 代码组织规范

#### 4.4.1 导入顺序
```typescript
// 1. React相关
import React, { useState, useEffect } from 'react';

// 2. 第三方库
import { Button, Table, Modal } from 'antd';
import styled from 'styled-components';

// 3. 内部模块 - 绝对路径
import { useApiCall } from '@/hooks';
import { KolCard } from '@/components/business';
import { formatDate } from '@/utils';

// 4. 相对路径
import { LocalComponent } from './components';
import type { ComponentProps } from './types';
```

#### 4.4.2 路径别名配置
```typescript
// vite.config.ts 或 tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/pages/*": ["src/pages/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"],
      "@/services/*": ["src/services/*"],
      "@/store/*": ["src/store/*"],
      "@/constants/*": ["src/constants/*"]
    }
  }
}
```

## 5. 性能与优化要求

### 5.1 代码分割
- 路由级别的懒加载
- 组件级别的动态导入
- 第三方库的按需加载

### 6.2 构建优化
- Tree shaking 启用
- 代码压缩与混淆
- 静态资源优化

## 7. 兼容性要求

### 7.1 浏览器支持
- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

### 7.2 Node.js 环境
- Node.js >= 16.0.0
- npm >= 8.0.0

## 8. 技术选型总结

以下技术选型为**强制要求**，必须严格遵循：

1. ✅ React v18+ + TypeScript
2. ✅ React Router v6
3. ✅ Redux Toolkit (RTK)
4. ✅ Ant Design v5 + Styled-components
5. ✅ ESLint + Prettier 代码规范
6. ✅ Vite 构建工具
7. ✅ Vitest + React Testing Library 测试框架

---

**文档维护：** 技术架构如有变更，需更新此文档并通知相关开发人员。
